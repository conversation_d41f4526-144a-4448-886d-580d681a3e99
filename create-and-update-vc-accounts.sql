-- VC账号创建和负责人更新脚本
-- 根据提供的数据创建VC账号并设置负责人

-- ========================================
-- 步骤1: 查找负责人用户ID
-- ========================================
SELECT '查找负责人用户ID' as message;

-- 查看系统中的用户信息，用于匹配负责人
SELECT 
    id as user_id,
    username,
    nickname,
    status
FROM system_users 
WHERE deleted = 0 
  AND status = 1
  AND (nickname LIKE '%丁凯%' OR nickname LIKE '%李明磊%' OR nickname LIKE '%张光烁%' 
       OR nickname LIKE '%郭梦含%' OR nickname LIKE '%曹嘉处%' OR nickname LIKE '%孟雪倩%')
ORDER BY nickname;

-- ========================================
-- 步骤2: 创建临时表存储VC账号数据
-- ========================================
DROP TEMPORARY TABLE IF EXISTS temp_vc_accounts;

CREATE TEMPORARY TABLE temp_vc_accounts (
    account_code VARCHAR(100),
    account_name VARCHAR(200),
    manager_name VARCHAR(100),
    manager_user_id BIGINT DEFAULT NULL
);

-- 插入VC账号数据
INSERT INTO temp_vc_accounts (account_code, account_name, manager_name) VALUES
-- 丁凯负责的账号
('xalbh668', '西安隆博海供应链', '丁凯'),
('lylw668', 'lylw668', '丁凯'),
('jzgyl888', 'jzgyl888', '丁凯'),
('zzktsm', '郑州柯鹏', '丁凯'),
('qgy668', '武汉福莱达888', '丁凯'),
('aet888666', 'aet888666', '丁凯'),
('jd85824565', '梅派', '丁凯'),
('nblqpop', '宁波沥泉贸易', '丁凯'),
('ywhc', '义乌海程', '丁凯'),
('shygj002', '上海御工匠', '丁凯'),
('hnhk888', 'hnhk888', '丁凯'),
('mt888', '美迢', '丁凯'),
('jfg8899', '建锋钢8899', '丁凯'),
('hngs001', '河南钢隋001', '丁凯'),
('jxyw666', 'jxyw666', '丁凯'),
('hdvc', '撼顿vc运营', '丁凯'),
('glqj', '恭良清洁用品', '丁凯'),
('sdgz', '山东工筑电子', '丁凯'),
('szdr888', 'szdr888', '丁凯'),
('drsmb', '鼎荣SMB', '丁凯'),
('guohao123179', 'guohao123179', '丁凯'),
('hq2022', '虎雀2022', '丁凯'),
('shsla', '上海圣兰奥电子商务', '丁凯'),
('zdh01', '做的好01', '丁凯'),
('ztdz', '筑唐电子', '丁凯'),
('jh08', '敬禾08', '丁凯'),
('gnzp01', '钢念臻品01', '丁凯'),
('shxy01', '上海锡豫01', '丁凯'),
('cpkp', '车品柯鹏', '丁凯'),

-- 李明磊负责的账号
('jfgjdjj', '建锋钢家电家居事业部', '李明磊'),
('lh2255', '龙恒2255', '李明磊'),
('jfgdschc', '建锋钢大商超慧采', '李明磊'),
('ys3c02', '云盛3C数码02', '李明磊'),
('zsysqc', '中山云盛汽车事业部', '李明磊'),
('zsysdschc', '中山云盛大商超慧采', '李明磊'),
('zzktsm2', '郑州旷田商贸', '李明磊'),
('hcjdjj', '慧采家电家居', '李明磊'),

-- 张光烁负责的账号
('shxzy002', 'shxzy002', '张光烁'),
('hcdzsw', '海程电子商务', '张光烁'),
('jyjc888', '隽佑建材888', '张光烁'),
('qw78899', '千唯78899', '张光烁'),
('mc459', '米层459', '张光烁'),
('mchwhc', '米层户外慧采', '张光烁'),
('fld3c', '福莱达3C数码', '张光烁'),
('flddsf', '福莱达大时尚', '张光烁'),
('mcjdjj', '米层家电家居', '张光烁'),

-- 郭梦含负责的账号
('sfldsc', '赛帆禄大商超', '郭梦含'),
('sfldqc', '赛帆禄大汽车', '郭梦含'),
('sfl3c', '赛帆禄3C', '郭梦含'),
('lwqchc', '莱维汽车慧采', '郭梦含'),
('ldqchc', '麟道汽车慧采', '郭梦含'),
('fqqchc', '丰谦汽车慧采', '郭梦含'),
('shld', '上海麟道', '郭梦含'),
('lbhnzhc', '隆博海农资慧采', '郭梦含'),
('hnsflqc', '河南赛帆禄汽车', '郭梦含'),
('yhtccp', '云汉天成车品供应', '郭梦含'),

-- 曹嘉处负责的账号
('ht9988166', '海棠9988166', '曹嘉处'),
('hnzy', '河南赞一', '曹嘉处'),
('hngs', '河南钢隋', '曹嘉处'),
('hndlhc', '河南迪黎慧采', '曹嘉处'),
('shsla6688', '上海圣兰奥6688', '曹嘉处'),
('hndl', '河南迪黎', '曹嘉处'),
('gsqcsyhc', '钢隋汽车事业部慧采', '曹嘉处'),
('slajj', '圣兰奥家居', '曹嘉处'),
('qbzqchc', '齐步走汽车慧采', '曹嘉处'),
('hnzyqchc', '河南赞一汽车慧采', '曹嘉处'),

-- 孟雪倩负责的账号（如果有的话，这里暂时为空，可以后续添加）
('mxq001', '孟雪倩测试账号', '孟雪倩');

-- ========================================
-- 步骤3: 更新临时表中的用户ID
-- ========================================
SELECT '更新负责人用户ID' as message;

-- 根据用户昵称匹配用户ID（需要根据实际用户表数据调整）
UPDATE temp_vc_accounts t
SET manager_user_id = (
    SELECT id FROM system_users 
    WHERE deleted = 0 AND status = 1 
    AND (nickname = t.manager_name OR username = t.manager_name)
    LIMIT 1
);

-- 显示匹配结果
SELECT 
    account_code,
    account_name,
    manager_name,
    manager_user_id,
    CASE WHEN manager_user_id IS NULL THEN '未找到用户' ELSE '已匹配' END as match_status
FROM temp_vc_accounts
ORDER BY manager_name, account_code;

-- ========================================
-- 步骤4: 创建不存在的VC账号
-- ========================================
SELECT '创建新的VC账号' as message;

INSERT INTO vc_account (
    account_code,
    account_name,
    account_type,
    manager_user_id,
    manager_user_name,
    status,
    priority_level,
    creator,
    create_time,
    updater,
    update_time,
    tenant_id
)
SELECT 
    t.account_code,
    t.account_name,
    '专营店' as account_type,
    t.manager_user_id,
    t.manager_name,
    1 as status,
    2 as priority_level,
    'system' as creator,
    NOW() as create_time,
    'system' as updater,
    NOW() as update_time,
    1 as tenant_id
FROM temp_vc_accounts t
WHERE NOT EXISTS (
    SELECT 1 FROM vc_account 
    WHERE account_code = t.account_code AND deleted = 0
)
AND t.manager_user_id IS NOT NULL;

SELECT CONCAT('新创建VC账号数量: ', ROW_COUNT()) as result;

-- ========================================
-- 步骤5: 更新已存在VC账号的负责人信息
-- ========================================
SELECT '更新已存在VC账号的负责人信息' as message;

UPDATE vc_account va
JOIN temp_vc_accounts t ON va.account_code = t.account_code
SET 
    va.manager_user_id = t.manager_user_id,
    va.manager_user_name = t.manager_name,
    va.updater = 'system',
    va.update_time = NOW()
WHERE va.deleted = 0 
  AND t.manager_user_id IS NOT NULL
  AND (va.manager_user_id IS NULL OR va.manager_user_id != t.manager_user_id);

SELECT CONCAT('更新VC账号负责人数量: ', ROW_COUNT()) as result;

-- ========================================
-- 步骤6: 验证结果
-- ========================================
SELECT '验证创建和更新结果' as message;

SELECT 
    va.account_code,
    va.account_name,
    va.manager_user_name,
    va.manager_user_id,
    u.nickname as user_nickname,
    va.status,
    va.create_time
FROM vc_account va
LEFT JOIN system_users u ON va.manager_user_id = u.id
WHERE va.account_code IN (
    SELECT account_code FROM temp_vc_accounts
)
AND va.deleted = 0
ORDER BY va.manager_user_name, va.account_code;

-- 显示未匹配到用户的账号
SELECT '未匹配到负责人的VC账号' as message;
SELECT 
    account_code,
    account_name,
    manager_name
FROM temp_vc_accounts 
WHERE manager_user_id IS NULL;

-- 清理临时表
DROP TEMPORARY TABLE temp_vc_accounts;

SELECT '脚本执行完成' as message;
