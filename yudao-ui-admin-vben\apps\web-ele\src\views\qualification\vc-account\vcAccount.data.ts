import type { VxeGridPropTypes } from '#/adapter/vxe-table'
import type { VbenFormSchema } from '@vben/common-ui'

import { h } from 'vue'

import { ElTag } from 'element-plus'

import { formatDate } from '@vben/utils'

/**
 * 表格列配置
 */
export function useGridColumns(): VxeGridPropTypes.Columns {
  return [
    { type: 'checkbox', width: 60 },
    {
      title: 'VC账号编码',
      field: 'accountCode',
      minWidth: 150,
      showOverflow: true,
    },
    {
      title: 'VC账号名称',
      field: 'accountName',
      minWidth: 200,
      showOverflow: true,
    },
    {
      title: '账号类型',
      field: 'accountType',
      minWidth: 120,
      showOverflow: true,
    },
    {
      title: '公司名称',
      field: 'companyName',
      minWidth: 200,
      showOverflow: true,
    },
    {
      title: '负责人',
      field: 'managerUserName',
      minWidth: 120,
      showOverflow: true,
    },
    {
      title: '联系电话',
      field: 'contactPhone',
      minWidth: 130,
      showOverflow: true,
    },
    {
      title: '资质数量',
      field: 'qualificationCount',
      minWidth: 100,
      cellRender: {
        name: 'VxeButton',
        props: {
          type: 'text',
          status: 'primary',
        },
        events: {
          click: ({ row }: any) => {
            console.log('查看资质:', row)
          },
        },
      },
    },
    {
      title: '即将过期',
      field: 'expiringCount',
      minWidth: 100,
      slots: {
        default: ({ row }: any) => {
          const count = row.expiringCount || 0
          return h(ElTag, {
            type: count > 0 ? ('warning' as const) : ('info' as const),
          }, () => count)
        },
      },
    },
    {
      title: '状态',
      field: 'status',
      minWidth: 80,
      slots: {
        default: ({ row }: any) => {
          return h(ElTag, {
            type: row.status === 1 ? ('success' as const) : ('danger' as const),
          }, () => row.status === 1 ? '正常' : '停用')
        },
      },
    },
    {
      title: '优先级',
      field: 'priorityLevel',
      minWidth: 80,
      slots: {
        default: ({ row }: any) => {
          const level = row.priorityLevel as 1 | 2 | 3
          const config = {
            1: { type: 'danger' as const, text: '高' },
            2: { type: 'warning' as const, text: '中' },
            3: { type: 'info' as const, text: '低' },
          }[level] || { type: 'info' as const, text: '未知' }

          return h(ElTag, { type: config.type }, () => config.text)
        },
      },
    },
    {
      title: '创建时间',
      field: 'createTime',
      minWidth: 180,
      formatter: ({ cellValue }: any) => {
        const formatted = formatDate(cellValue)
        return typeof formatted === 'string' ? formatted : String(formatted || '')
      },
      sortable: true,
    },
  ]
}

/**
 * 搜索表单配置
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入VC账号编码',
        clearable: true,
      },
      fieldName: 'accountCode',
      label: 'VC账号编码',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入VC账号名称',
        clearable: true,
      },
      fieldName: 'accountName',
      label: 'VC账号名称',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择账号类型',
        clearable: true,
        options: [
          { label: '旗舰店', value: '旗舰店' },
          { label: '专营店', value: '专营店' },
          { label: '代理商', value: '代理商' },
          { label: '直营店', value: '直营店' },
          { label: '加盟店', value: '加盟店' },
        ],
      },
      fieldName: 'accountType',
      label: '账号类型',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入负责人姓名',
        clearable: true,
      },
      fieldName: 'managerUserName',
      label: '负责人',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        clearable: true,
        options: [
          { label: '正常', value: 1 },
          { label: '停用', value: 0 },
        ],
      },
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'DatePicker',
      componentProps: {
        type: 'datetimerange',
        placeholder: ['开始时间', '结束时间'],
        clearable: true,
      },
      fieldName: 'createTime',
      label: '创建时间',
    },
  ]
}

/**
 * 表单配置
 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入VC账号编码',
      },
      fieldName: 'accountCode',
      label: 'VC账号编码',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入VC账号名称',
      },
      fieldName: 'accountName',
      label: 'VC账号名称',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择账号类型',
        options: [
          { label: '旗舰店', value: '旗舰店' },
          { label: '专营店', value: '专营店' },
          { label: '代理商', value: '代理商' },
          { label: '直营店', value: '直营店' },
          { label: '加盟店', value: '加盟店' },
        ],
      },
      fieldName: 'accountType',
      label: '账号类型',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入公司名称',
      },
      fieldName: 'companyName',
      label: '公司名称',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入联系人',
      },
      fieldName: 'contactPerson',
      label: '联系人',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入联系电话',
      },
      fieldName: 'contactPhone',
      label: '联系电话',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入联系邮箱',
      },
      fieldName: 'contactEmail',
      label: '联系邮箱',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入经营范围',
        rows: 3,
      },
      fieldName: 'businessScope',
      label: '经营范围',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入区域编码',
      },
      fieldName: 'regionCode',
      label: '所属区域编码',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入区域名称',
      },
      fieldName: 'regionName',
      label: '区域名称',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入负责人用户ID',
      },
      fieldName: 'managerUserId',
      label: '负责人用户ID',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入备用负责人用户ID',
      },
      fieldName: 'backupManagerUserId',
      label: '备用负责人用户ID',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '高', value: 1 },
          { label: '中', value: 2 },
          { label: '低', value: 3 },
        ],
      },
      fieldName: 'priorityLevel',
      label: '优先级',
      defaultValue: 3,
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '正常', value: 1 },
          { label: '停用', value: 0 },
        ],
      },
      fieldName: 'status',
      label: '状态',
      defaultValue: 1,
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入备注',
        rows: 3,
      },
      fieldName: 'remark',
      label: '备注',
    },
  ]
}
